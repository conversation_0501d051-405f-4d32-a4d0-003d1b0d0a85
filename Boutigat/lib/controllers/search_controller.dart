import 'package:get/get.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/search.dart';
import 'package:boutigak/data/services/search_service.dart';



class MYSearchController extends GetxController {
  // --- State principal ---
  final searchText = ''.obs;
  final items = <Item>[].obs;

  final searchHistory = <SearchHistory>[].obs;
  final popularCategories = <String>[].obs;

  final showHistory = true.obs;

  // --- Loaders pour l’UI ---
  final isLoadingItems = false.obs;
  final isLoadingHistory = false.obs;
  final isLoadingPopular = false.obs;

  // --- Current locale tracking ---
  String? _currentLanguageCode;

  // ========== INPUT ==========
  void onSearchChanged(String value) {
    searchText.value = value;

    // Si l’input est vidé → on réaffiche l’historique et on efface les résultats
    if (value.trim().isEmpty) {
      showHistory.value = true;
      items.clear();
      //update();
    }
  }

  // ========== RECHERCHE ==========
  Future<void> submitSearch() async {
    final q = searchText.value.trim();
    if (q.isEmpty) {
      showHistory.value = true;
      items.clear();
    //  update();
      return;
    }

    showHistory.value = false;
    isLoadingItems.value = true;

    try {
      final result = await SearchService.searchItems(q);
      if (result != null) {
        items.assignAll(result);
      } else {
        items.clear();
      }
      // (facultatif) rafraîchir l’historique après une recherche
      fetchSearchHistory();
    } catch (e) {
      // log/handle si besoin
      items.clear();
    } finally {
      isLoadingItems.value = false;
    //  update();
    }
  }

  void submitHistorySearch(String query) {
    searchText.value = query;
    submitSearch();
  }

  void clearSearch() {
    searchText.value = '';
    items.clear();
    showHistory.value = true;
  //  update();
  }

  // ========== HISTORIQUE ==========
  Future<void> fetchSearchHistory() async {
    isLoadingHistory.value = true;
    try {
      final result = await SearchService.fetchSearchHistory();
      if (result != null) {
        searchHistory.assignAll(result);
      } else {
        searchHistory.clear();
      }
    } catch (e) {
      // log/handle si besoin
    } finally {
      isLoadingHistory.value = false;
   //   update();
    }
  }

  Future<void> removeSearchHistory(int id) async {
    try {
      final success = await SearchService.deleteSearchHistory(id);
      if (success) {
        searchHistory.removeWhere((item) => item.id == id);
        update();
      }
    } catch (e) {
      // log/handle
    }
  }

  // ========== POPULAR ==========
  Future<void> getPopularCategories() async {
    isLoadingPopular.value = true;
    try {
      final result = await SearchService.fetchPopuarCategories();
      if (result == null) {
        popularCategories.clear();
        return;
      }

      // Détermination du champ selon la langue
      final languageCode = Get.locale?.languageCode ?? 'en';
      late final String titleField;
      switch (languageCode) {
        case 'ar':
          titleField = 'title_ar';
          break;
        case 'fr':
          titleField = 'title_fr';
          break;
        default:
          titleField = 'title_en';
      }

      // Extraction + nettoyage (null-safe, trim, uniques)
      final list = result
          .map<String?>((e) => e[titleField] as String?)
          .where((s) => s != null && s.trim().isNotEmpty)
          .map((s) => s!.trim())
          .toSet() // uniques
          .toList();

      popularCategories.assignAll(list);
    } catch (e) {
      popularCategories.clear();
    } finally {
      isLoadingPopular.value = false;
   //   update();
    }
  }

  // ========== CYCLES ==========
  @override
  void onInit() {
    super.onInit();
    // Set initial language code
    _currentLanguageCode = Get.locale?.languageCode ?? 'en';

    // Chargements initiaux
    refreshSearchData();
  }

  // Method to refresh popular categories when language changes
  void refreshPopularCategoriesOnLanguageChange() {
    final newLanguageCode = Get.locale?.languageCode ?? 'en';
    if (_currentLanguageCode != newLanguageCode) {
      _currentLanguageCode = newLanguageCode;
      getPopularCategories();
    }
  }

  void refreshSearchData() {
    fetchSearchHistory();
    getPopularCategories();
    update();
  }
}
